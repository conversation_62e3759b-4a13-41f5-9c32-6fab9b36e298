import pygame
import json
import time
from background import Background
from ui_components import <PERSON><PERSON>utton, CarCard
from valuation_system import valuation_system
from parts_requirements import parts_requirements
from utils import load_font, resource_path
from cursor_manager import cursor_manager
from language_manager import get_language_manager
from fuel_system import fuel_system

class SellPartDialog:
    def __init__(self, part_data, selling_price, s_width, s_height):
        self.part_data = part_data
        self.selling_price = selling_price

        # Dialog dimensions
        self.width = 400
        self.height = 200
        self.rect = pygame.Rect(
            (s_width - self.width) // 2,
            (s_height - self.height) // 2,
            self.width,
            self.height
        )

        # Create fonts
        self.header_font = load_font("arial", 24)
        self.font = load_font("arial", 20)

        # Create buttons
        button_y = self.rect.y + self.height - 60
        self.sell_button = pygame.Rect(self.rect.x + 50, button_y, 100, 40)
        self.cancel_button = pygame.Rect(self.rect.x + self.width - 150, button_y, 100, 40)

        # Anti-misclick protection
        self.creation_time = pygame.time.get_ticks()
        self.min_interaction_delay = 300  # 300ms delay for part selling
        self.last_click_time = 0
        self.click_debounce_delay = 150  # 150ms between clicks

    def update(self, mouse_pos, mouse_click):
        current_time = pygame.time.get_ticks()

        # Prevent interactions too soon after dialog creation
        if current_time - self.creation_time < self.min_interaction_delay:
            return None

        # Debounce clicks
        if mouse_click[0] and current_time - self.last_click_time > self.click_debounce_delay:
            self.last_click_time = current_time
            if self.sell_button.collidepoint(mouse_pos):
                return "sell"
            elif self.cancel_button.collidepoint(mouse_pos):
                return "cancel"
        return None

    def handle_event(self, event, mouse_pos):
        """Handle mouse events - preferred method"""
        current_time = pygame.time.get_ticks()

        # Prevent interactions too soon after dialog creation
        if current_time - self.creation_time < self.min_interaction_delay:
            return None

        if event.type == pygame.MOUSEBUTTONDOWN and event.button == 1:  # Left click
            # Debounce clicks
            if current_time - self.last_click_time > self.click_debounce_delay:
                self.last_click_time = current_time
                if self.sell_button.collidepoint(mouse_pos):
                    return "sell"
                elif self.cancel_button.collidepoint(mouse_pos):
                    return "cancel"
        return None

    def draw(self, screen):
        lang_manager = get_language_manager()

        # Dialog background
        pygame.draw.rect(screen, (40, 40, 40), self.rect)
        pygame.draw.rect(screen, (200, 200, 200), self.rect, 2)

        # Header
        header_text = self.header_font.render(lang_manager.get_text("shop.confirm_sale"), True, (255, 255, 255))
        header_x = self.rect.x + (self.width - header_text.get_width()) // 2
        screen.blit(header_text, (header_x, self.rect.y + 20))

        # Part name
        part_name = self.font.render(f"{lang_manager.get_text('parts.part_name')}: {self.part_data['name']}", True, (200, 200, 200))
        screen.blit(part_name, (self.rect.x + 20, self.rect.y + 60))

        # Selling price
        price_text = self.font.render(f"{lang_manager.get_text('shop.price')}: {self.selling_price} {lang_manager.get_text('units.currency')}", True, (0, 255, 0))
        screen.blit(price_text, (self.rect.x + 20, self.rect.y + 90))

        # Buttons
        current_time = pygame.time.get_ticks()
        buttons_enabled = current_time - self.creation_time >= self.min_interaction_delay

        button_texts = [lang_manager.get_text("shop.sell"), lang_manager.get_text("ui.cancel")]
        for i, (button, base_color) in enumerate([(self.sell_button, (0, 200, 0)), (self.cancel_button, (200, 0, 0))]):
            text = button_texts[i]
            # Dim buttons if not yet enabled
            if buttons_enabled:
                color = base_color
                text_color = (255, 255, 255)
            else:
                color = tuple(c // 3 for c in base_color)  # Darker version
                text_color = (150, 150, 150)

            pygame.draw.rect(screen, color, button)
            pygame.draw.rect(screen, (255, 255, 255), button, 2)

            button_text = self.font.render(text, True, text_color)
            text_x = button.x + (button.width - button_text.get_width()) // 2
            text_y = button.y + (button.height - button_text.get_height()) // 2
            screen.blit(button_text, (text_x, text_y))

        # Show countdown if buttons are not yet enabled
        if not buttons_enabled:
            remaining_time = (self.min_interaction_delay - (current_time - self.creation_time)) / 1000.0
            countdown_text = load_font("arial", 16).render(f"{lang_manager.get_text('messages.please_wait')} {remaining_time:.1f}s", True, (255, 200, 100))
            text_x = self.rect.x + (self.width - countdown_text.get_width()) // 2
            text_y = self.rect.y + self.height - 90
            screen.blit(countdown_text, (text_x, text_y))

class PartCard:
    def __init__(self, part_data, x, y, width, height, is_equipped=False, usage_data=None):
        self.part_data = part_data
        self.rect = pygame.Rect(x, y, width, height)
        self.is_equipped = is_equipped
        self.is_hovered = False
        self.font = load_font("arial", 20)
        self.header_font = load_font("arial", 24)
        self.small_font = load_font("arial", 16) # Added missing small_font
        self.usage_data = usage_data or valuation_system.get_default_usage_data()

        # Create sell button for non-equipped parts
        if not is_equipped:
            self.sell_button = pygame.Rect(x + width - 60, y + height - 30, 50, 25)
        else:
            self.sell_button = None
        self.sell_button_hovered = False

        # Anti-misclick protection for part interactions
        self.last_click_time = 0
        self.click_debounce_delay = 250  # 250ms between part clicks
        self.last_sell_click_time = 0
        self.sell_debounce_delay = 400  # 400ms for selling parts (more critical)

    def update(self, mouse_pos, mouse_click):
        self.is_hovered = self.rect.collidepoint(mouse_pos)
        current_time = pygame.time.get_ticks()

        # Check sell button hover
        if self.sell_button:
            self.sell_button_hovered = self.sell_button.collidepoint(mouse_pos)
        else:
            self.sell_button_hovered = False

        # Register hover state with cursor manager
        is_hovering_interactive = self.is_hovered or self.sell_button_hovered
        cursor_manager.register_hoverable(is_hovering_interactive)

        # Check sell button click with debouncing
        if self.sell_button and self.sell_button_hovered and mouse_click[0]:
            if current_time - self.last_sell_click_time > self.sell_debounce_delay:
                self.last_sell_click_time = current_time
                return "sell"

        # Check main card click (for equipping) with debouncing
        if self.is_hovered and mouse_click[0] and not (self.sell_button and self.sell_button_hovered):
            if current_time - self.last_click_time > self.click_debounce_delay:
                self.last_click_time = current_time
                return "equip"

        return None

    def handle_event(self, event, mouse_pos):
        """Handle mouse events - preferred method"""
        self.is_hovered = self.rect.collidepoint(mouse_pos)
        current_time = pygame.time.get_ticks()

        # Check sell button hover
        if self.sell_button:
            self.sell_button_hovered = self.sell_button.collidepoint(mouse_pos)
        else:
            self.sell_button_hovered = False

        # Register hover state with cursor manager
        is_hovering_interactive = self.is_hovered or self.sell_button_hovered
        cursor_manager.register_hoverable(is_hovering_interactive)

        if event.type == pygame.MOUSEBUTTONDOWN and event.button == 1:  # Left click
            # Check sell button click with debouncing
            if self.sell_button and self.sell_button_hovered:
                if current_time - self.last_sell_click_time > self.sell_debounce_delay:
                    self.last_sell_click_time = current_time
                    return "sell"

            # Check main card click (for equipping) with debouncing
            if self.is_hovered and not (self.sell_button and self.sell_button_hovered):
                if current_time - self.last_click_time > self.click_debounce_delay:
                    self.last_click_time = current_time
                    return "equip"

        return None

    def draw(self, screen):
        # Draw card background
        if self.is_equipped:
            bg_color = (20, 80, 20) if self.is_hovered else (10, 60, 10)
            border_color = (0, 255, 0)
        else:
            bg_color = (60, 60, 60) if self.is_hovered else (40, 40, 40)
            border_color = (200, 200, 200)
        
        pygame.draw.rect(screen, bg_color, self.rect)
        pygame.draw.rect(screen, border_color, self.rect, 1)
        
        # Draw part info
        y_offset = self.rect.y + 10
        
        # Part name
        name_color = (150, 255, 150) if self.is_equipped else (255, 255, 255)
        name_text = self.header_font.render(self.part_data["name"], True, name_color)
        screen.blit(name_text, (self.rect.x + 10, y_offset))
        y_offset += 30
        
        # Show "EQUIPPED" label if equipped
        if self.is_equipped:
            lang_manager = get_language_manager()
            equipped_text = self.font.render(lang_manager.get_text("garage.part_installed").upper(), True, (0, 255, 0))
            screen.blit(equipped_text, (self.rect.x + 10, y_offset))
            y_offset += 25
        
        # Part stats
        lang_manager = get_language_manager()
        stats = []
        # Show base horsepower for engine part
        if "horsepower" in self.part_data and self.part_data.get("type") == "engine":
            stats.append(f"{lang_manager.get_text('garage.engine_power')}: {self.part_data['horsepower']} {lang_manager.get_text('units.hp')}")
        elif "horsepower" in self.part_data:
            stats.append(f"{lang_manager.get_text('garage.power')}: {self.part_data['horsepower']} {lang_manager.get_text('units.hp')}")
        if "horsepower_boost_percentage" in self.part_data:
            stats.append(f"{lang_manager.get_text('garage.boost')}: +{self.part_data['horsepower_boost_percentage']}%")
        if "weight" in self.part_data:
            stats.append(f"{lang_manager.get_text('garage.weight')}: {self.part_data['weight']} {lang_manager.get_text('units.kg')}")
        
        for stat in stats:
            stat_text = self.font.render(stat, True, (200, 200, 200))
            screen.blit(stat_text, (self.rect.x + 10, y_offset))
            y_offset += 20

        # Part value with condition
        base_value = self.part_data.get('value', 0)
        part_type = self.part_data.get('category', 'engine')  # Default to engine if no category

        # Calculate current value based on usage
        current_value = valuation_system.calculate_part_value(
            self.part_data,
            part_type,
            self.usage_data.get(f"{part_type}_age_days", 0),
            self.usage_data.get("races_completed", 0)
        )

        condition = valuation_system.calculate_condition(
            self.usage_data.get(f"{part_type}_age_days", 0),
            self.usage_data.get("races_completed", 0),
            part_type
        )

        condition_percentage = int(condition * 100)
        value_text = self.small_font.render(f"{lang_manager.get_text('garage.value')}: {int(current_value)} {lang_manager.get_text('units.currency')} ({condition_percentage}%)", True, (150, 150, 150))
        screen.blit(value_text, (self.rect.x + 10, self.rect.y + self.rect.height - 45))

        # Draw sell button for non-equipped parts
        if self.sell_button:
            # Calculate selling price (70% of current value)
            selling_price = int(current_value * 0.7)

            # Button color based on hover
            button_color = (80, 40, 40) if self.sell_button_hovered else (60, 30, 30)
            border_color = (255, 100, 100) if self.sell_button_hovered else (200, 80, 80)

            pygame.draw.rect(screen, button_color, self.sell_button)
            pygame.draw.rect(screen, border_color, self.sell_button, 1)

            # Button text
            sell_text = self.small_font.render(lang_manager.get_text("shop.sell"), True, (255, 255, 255))
            text_x = self.sell_button.x + (self.sell_button.width - sell_text.get_width()) // 2
            text_y = self.sell_button.y + (self.sell_button.height - sell_text.get_height()) // 2
            screen.blit(sell_text, (text_x, text_y))

            # Show selling price
            price_text = self.small_font.render(f"{lang_manager.get_text('shop.price')}: {selling_price}{lang_manager.get_text('units.currency')}", True, (200, 200, 100))
            screen.blit(price_text, (self.rect.x + 10, self.rect.y + self.rect.height - 25))

class CategoryTab:
    def __init__(self, label, x, y, width, height):
        self.rect = pygame.Rect(x, y, width, height)
        self.label = label
        self.is_active = False
        self.is_hovered = False
        self.font = load_font("arial", 20)
    
    def update(self, mouse_pos, mouse_click=None):
        self.is_hovered = self.rect.collidepoint(mouse_pos)

        # Register hover state with cursor manager
        cursor_manager.register_hoverable(self.is_hovered)

        # Legacy support for mouse_click parameter (deprecated)
        if mouse_click is not None and self.is_hovered and mouse_click[0]:
            return True
        return False

    def handle_event(self, event, mouse_pos):
        """Handle mouse events - preferred method"""
        self.is_hovered = self.rect.collidepoint(mouse_pos)

        # Register hover state with cursor manager
        cursor_manager.register_hoverable(self.is_hovered)

        if event.type == pygame.MOUSEBUTTONDOWN and event.button == 1:  # Left click
            if self.is_hovered:
                return True
        return False

    def draw(self, screen):
        if self.is_active:
            bg_color = (0, 255, 255)
            text_color = (0, 0, 0)
        elif self.is_hovered:
            bg_color = (80, 80, 80)
            text_color = (255, 255, 255)
        else:
            bg_color = (50, 50, 50)
            text_color = (200, 200, 200)
        
        pygame.draw.rect(screen, bg_color, self.rect)
        pygame.draw.rect(screen, (200, 200, 200), self.rect, 1)
        
        text = self.font.render(self.label, True, text_color)
        text_x = self.rect.x + (self.rect.width - text.get_width()) // 2
        text_y = self.rect.y + (self.rect.height - text.get_height()) // 2
        screen.blit(text, (text_x, text_y))

def save_car_parts(car_index, equipped_parts):
    """Save equipped parts to garage.json"""
    try:
        # Validate car index
        if car_index < 0:
            raise ValueError("Invalid car index")

        # Load garage data
        with open(resource_path('data/garage.json'), 'r', encoding='utf-8') as f:
            cars_data = json.load(f)

        # Validate car index bounds
        if car_index >= len(cars_data):
            raise ValueError(f"Car index {car_index} out of bounds (max: {len(cars_data)-1})")

        # Validate equipped parts structure
        if not isinstance(equipped_parts, dict):
            raise ValueError("Equipped parts must be a dictionary")

        # Update the car's parts
        cars_data[car_index]['parts'] = equipped_parts

        # Save garage data
        with open(resource_path('data/garage.json'), 'w', encoding='utf-8') as f:
            json.dump(cars_data, f, indent=4)

        # Auto-save if current save slot is set
        from save_system import save_system
        if save_system.current_save_slot:
            save_system.save_game(save_system.current_save_slot)

        return True

    except FileNotFoundError:
        print("Error: Garage file not found")
        return False
    except json.JSONDecodeError:
        print("Error: Garage file is corrupted")
        return False
    except Exception as e:
        print(f"Error saving car parts: {e}")
        return False

def sell_part(part_data, part_category, usage_data):
    """Sell a part and update player money and inventory"""
    try:
        # Calculate selling price using valuation system
        part_type = part_data.get('category', part_category)
        current_value = valuation_system.calculate_part_value(
            part_data,
            part_type,
            usage_data.get(f"{part_type}_age_days", 0),
            usage_data.get("races_completed", 0)
        )
        selling_price = int(current_value * 0.7)  # 70% of current value

        # Load profile data
        with open(resource_path('data/profile.json'), 'r', encoding='utf-8') as f:
            profile_data = json.load(f)

        # Add money to player
        profile_data['money'] += selling_price

        # Remove part from inventory
        owned_parts = profile_data['inventory']['owned_parts'].get(part_category, [])
        if part_data['name'] in owned_parts:
            owned_parts.remove(part_data['name'])

        # Save profile data
        with open(resource_path('data/profile.json'), 'w', encoding='utf-8') as f:
            json.dump(profile_data, f, indent=4)

        # Auto-save if current save slot is set
        from save_system import save_system
        if save_system.current_save_slot:
            save_system.save_game(save_system.current_save_slot)

        return True, selling_price

    except Exception as e:
        return False, 0


def install_part(car_index, part_data, part_category):
    """Install a part on a specific car with validation and performance update"""
    try:
        # Validate inputs
        if not isinstance(part_data, dict) or 'name' not in part_data:
            raise ValueError("Invalid part data")

        if part_category not in ['engine', 'turbo', 'intercooler', 'ecu']:
            raise ValueError(f"Invalid part category: {part_category}")

        # Load garage data
        with open(resource_path('data/garage.json'), 'r', encoding='utf-8') as f:
            cars_data = json.load(f)

        # Validate car index
        if car_index < 0 or car_index >= len(cars_data):
            raise ValueError(f"Invalid car index: {car_index}")

        # Load profile data to check ownership
        with open(resource_path('data/profile.json'), 'r', encoding='utf-8') as f:
            profile_data = json.load(f)

        # Check if player owns this part
        owned_parts = profile_data.get('inventory', {}).get('owned_parts', {}).get(part_category, [])
        if part_data['name'] not in owned_parts:
            raise ValueError(f"Player does not own part: {part_data['name']}")

        # Install the part
        cars_data[car_index]['parts'][part_category] = part_data

        # Save garage data
        with open(resource_path('data/garage.json'), 'w', encoding='utf-8') as f:
            json.dump(cars_data, f, indent=4)

        # Auto-save if current save slot is set
        from save_system import save_system
        if save_system.current_save_slot:
            save_system.save_game(save_system.current_save_slot)

        return True, f"Successfully installed {part_data['name']}"

    except Exception as e:
        return False, str(e)


def uninstall_part(car_index, part_category):
    """Uninstall a part from a specific car"""
    try:
        # Validate inputs
        if part_category not in ['engine', 'turbo', 'intercooler', 'ecu']:
            raise ValueError(f"Invalid part category: {part_category}")

        # Load garage data
        with open(resource_path('data/garage.json'), 'r', encoding='utf-8') as f:
            cars_data = json.load(f)

        # Validate car index
        if car_index < 0 or car_index >= len(cars_data):
            raise ValueError(f"Invalid car index: {car_index}")

        # Get current part name for feedback
        current_part = cars_data[car_index]['parts'].get(part_category)
        part_name = current_part['name'] if current_part else "No part"

        # Uninstall the part (set to None)
        cars_data[car_index]['parts'][part_category] = None

        # Save garage data
        with open(resource_path('data/garage.json'), 'w', encoding='utf-8') as f:
            json.dump(cars_data, f, indent=4)

        # Auto-save if current save slot is set
        from save_system import save_system
        if save_system.current_save_slot:
            save_system.save_game(save_system.current_save_slot)

        return True, f"Successfully uninstalled {part_name}"

    except Exception as e:
        return False, str(e)


def validate_part_compatibility(car_data, part_data, part_category):
    """Validate if a part is compatible with a specific car using the CarCompatibility system"""
    from car_compatibility import car_compatibility

    # Basic validation
    if not isinstance(part_data, dict):
        return False, "Invalid part data"

    if 'name' not in part_data or 'category' not in part_data:
        return False, "Part missing required fields"

    if part_data['category'] != part_category:
        return False, f"Part category mismatch: expected {part_category}, got {part_data['category']}"

    # Engine-specific validation
    if part_category == 'engine':
        if 'horsepower' not in part_data or 'weight' not in part_data:
            return False, "Engine missing horsepower or weight data"

    # Turbo/Intercooler/ECU validation
    elif part_category in ['turbo', 'intercooler', 'ecu']:
        if 'horsepower_boost_percentage' not in part_data:
            return False, f"{part_category} missing horsepower boost data"

    # Get car name for compatibility check
    car_name = car_data.get('name', 'unknown')
    part_name = part_data.get('name', 'unknown')

    # Use CarCompatibility system to validate part compatibility
    is_compatible, compatibility_reason = car_compatibility.is_part_compatible(
        car_name, part_name, part_category
    )

    if not is_compatible:
        return False, compatibility_reason

    return True, "Part is compatible"


def get_installation_preview(car_data, new_parts):
    """Calculate performance preview with new parts installed"""
    try:
        # Create a copy of car data with new parts
        preview_car = car_data.copy()
        preview_car['parts'] = car_data['parts'].copy()

        # Apply new parts
        for category, part in new_parts.items():
            if part is not None:
                preview_car['parts'][category] = part

        # Calculate performance with new parts
        performance_data = valuation_system.calculate_enhanced_performance(
            preview_car,
            valuation_system.get_default_usage_data()
        )

        return {
            'total_horsepower': performance_data['total_horsepower'],
            'total_weight': performance_data['total_weight'],
            'power_to_weight_ratio': performance_data['total_horsepower'] / performance_data['total_weight'] if performance_data['total_weight'] > 0 else 0,
            'estimated_performance_gain': performance_data['total_horsepower'] - valuation_system.calculate_enhanced_performance(car_data, valuation_system.get_default_usage_data())['total_horsepower']
        }

    except Exception as e:
        return None


def fix_car_data_consistency():
    """Fix inconsistencies between profile.json and garage.json"""
    try:
        with open(resource_path('data/profile.json'), 'r', encoding='utf-8') as f:
            profile_data = json.load(f)
        with open(resource_path('data/garage.json'), 'r', encoding='utf-8') as f:
            garage_data = json.load(f)

        owned_cars = profile_data.get("inventory", {}).get("owned_cars", [])
        selected_car = profile_data.get("cars", {}).get("selected_car", 0)
        changes_made = False

        # Check for inconsistencies
        if len(owned_cars) != len(garage_data):
            print(f"Data inconsistency detected: {len(owned_cars)} owned cars vs {len(garage_data)} garage entries")

            # If garage has fewer cars than owned_cars claims, fix owned_cars
            if len(garage_data) < len(owned_cars):
                # Update owned_cars to match garage_data
                new_owned_cars = []
                for i, car_data in enumerate(garage_data):
                    new_owned_cars.append(car_data["name"])

                profile_data["inventory"]["owned_cars"] = new_owned_cars

                # Fix selected_car index
                if selected_car >= len(new_owned_cars):
                    profile_data["cars"]["selected_car"] = max(0, len(new_owned_cars) - 1) if new_owned_cars else -1

                # Color system removed - no car_colors cleanup needed

                changes_made = True

        # Validate and fix system data for all cars
        for i in range(len(garage_data)):
            car_key = str(i)

            # Ensure usage_data exists
            if "usage_data" not in profile_data:
                profile_data["usage_data"] = {"cars": {}}
            if "cars" not in profile_data["usage_data"]:
                profile_data["usage_data"]["cars"] = {}
            if car_key not in profile_data["usage_data"]["cars"]:
                from valuation_system import valuation_system
                profile_data["usage_data"]["cars"][car_key] = valuation_system.get_default_usage_data()
                changes_made = True

            # Ensure fuel_data exists
            if "fuel_data" not in profile_data:
                profile_data["fuel_data"] = {"cars": {}}
            if "cars" not in profile_data["fuel_data"]:
                profile_data["fuel_data"]["cars"] = {}
            if car_key not in profile_data["fuel_data"]["cars"]:
                profile_data["fuel_data"]["cars"][car_key] = {
                    "current_fuel": 60,
                    "max_capacity": 60,
                    "last_refuel": int(time.time())
                }
                changes_made = True

            # Ensure tire_data exists
            if "tire_data" not in profile_data:
                profile_data["tire_data"] = {"cars": {}}
            if "cars" not in profile_data["tire_data"]:
                profile_data["tire_data"]["cars"] = {}
            if car_key not in profile_data["tire_data"]["cars"]:
                profile_data["tire_data"]["cars"][car_key] = {
                    "tire_type": "standard",
                    "condition": 100.0,
                    "total_distance": 0.0,
                    "last_replacement": int(time.time())
                }
                changes_made = True

            # Ensure maintenance_data exists
            if "maintenance_data" not in profile_data:
                profile_data["maintenance_data"] = {"cars": {}, "insurance": None}
            if "cars" not in profile_data["maintenance_data"]:
                profile_data["maintenance_data"]["cars"] = {}
            if car_key not in profile_data["maintenance_data"]["cars"]:
                profile_data["maintenance_data"]["cars"][car_key] = {
                    "last_maintenance": int(time.time()),
                    "last_maintenance_races": 0,
                    "maintenance_due": False,
                    "total_maintenance_cost": 0,
                    "crashes": 0,
                    "repair_history": []
                }
                changes_made = True

        # Fix missing required parts in garage cars
        cars_fixed, parts_added = parts_requirements.fix_all_cars_in_garage()
        if cars_fixed > 0:
            print(f"Fixed {cars_fixed} cars with missing required parts: {parts_added}")
            changes_made = True

        # Save fixed profile if changes were made
        if changes_made:
            with open(resource_path('data/profile.json'), 'w', encoding='utf-8') as f:
                json.dump(profile_data, f, indent=4)
            print("Fixed data inconsistency and missing system data")

        return changes_made

    except Exception as e:
        print(f"Error fixing car data consistency: {e}")
        return False

def draw_enhanced_garage_screen(s_width, s_height, screen):
    run = True
    bg = Background('background', s_width, s_height)
    lang_manager = get_language_manager()

    # Fix any data inconsistencies first
    fix_car_data_consistency()

    # Load data with error handling
    try:
        with open(resource_path('data/garage.json'), 'r', encoding='utf-8') as f:
            cars_data = json.load(f)
    except (FileNotFoundError, json.JSONDecodeError) as e:
        print(f"Error loading garage data: {e}")
        return

    try:
        with open(resource_path('data/shop_data.json'), 'r', encoding='utf-8') as f:
            shop_data = json.load(f)
        parts_data = shop_data[0]
    except (FileNotFoundError, json.JSONDecodeError) as e:
        print(f"Error loading shop data: {e}")
        return

    try:
        with open(resource_path('data/profile.json'), 'r', encoding='utf-8') as f:
            profile_data = json.load(f)
    except (FileNotFoundError, json.JSONDecodeError) as e:
        print(f"Error loading profile data: {e}")
        return
    
    # Create buttons
    back_button = TextButton(lang_manager.get_text('menu.back'), 50, 50, font_size=36)
    save_button = TextButton(lang_manager.get_text('ui.save'), s_width - 200, 50, font_size=36)
    refuel_button = TextButton(lang_manager.get_text('fuel.refuel_button'), s_width - 400, 50, font_size=36)

    # Get selected car
    selected_car_index = profile_data["cars"].get("selected_car", -1)
    owned_cars = profile_data.get("inventory", {}).get("owned_cars", [])
    if not owned_cars or selected_car_index < 0 or selected_car_index >= len(owned_cars):
        # No cars owned or invalid index, handle gracefully
        selected_car_index = -1
    
    if selected_car_index == -1:
        # No car to display, show message to buy a car
        selected_car = None
        car_card = None

        # Show message to user
        font = load_font("arial", 36)
        no_car_message = font.render(lang_manager.get_text('garage.no_car_owned'), True, (255, 100, 100))
        buy_car_message = font.render(lang_manager.get_text('garage.go_to_shop'), True, (200, 200, 200))

        # Early return with message
        while run:
            mouse_pos = pygame.mouse.get_pos()

            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    return
                if event.type == pygame.KEYDOWN and event.key == pygame.K_ESCAPE:
                    return

                # Handle button events
                if back_button.handle_event(event, mouse_pos):
                    return

            # Update button hover states
            back_button.update(mouse_pos)

            bg.draw(screen)

            # Draw header
            header = load_font("arial", 48).render(lang_manager.get_text('garage.tuning_garage'), True, (255, 255, 255))
            screen.blit(header, (s_width // 2 - header.get_width() // 2, 50))

            # Draw no car message
            screen.blit(no_car_message, (s_width // 2 - no_car_message.get_width() // 2, s_height // 2 - 50))
            screen.blit(buy_car_message, (s_width // 2 - buy_car_message.get_width() // 2, s_height // 2 + 10))

            back_button.draw(screen)
            pygame.display.update()

        return
    else:
        # Validate selected car index against garage data
        if selected_car_index >= len(cars_data):
            print(f"Error: Selected car index {selected_car_index} exceeds garage data length {len(cars_data)}")
            selected_car_index = -1
            selected_car = None
            car_card = None
        else:
            selected_car = cars_data[selected_car_index]

        car_card = CarCard(selected_car, 50, 150, 300, 350, True)

    # Part categories
    categories = ["engine", "turbo", "intercooler", "ecu"]
    category_names = {
        "engine": lang_manager.get_text('parts.engine'),
        "turbo": lang_manager.get_text('parts.turbo'),
        "intercooler": lang_manager.get_text('parts.intercooler'),
        "ecu": lang_manager.get_text('parts.ecu')
    }
    
    # Create category tabs
    tab_width = 100
    tab_height = 30
    tabs_per_row = 5
    tab_start_x = 400
    tab_start_y = 150
    
    category_tabs = []
    for i, category in enumerate(categories):
        row = i // tabs_per_row
        col = i % tabs_per_row
        x = tab_start_x + col * (tab_width + 10)
        y = tab_start_y + row * (tab_height + 10)
        tab = CategoryTab(category_names[category], x, y, tab_width, tab_height)
        category_tabs.append((category, tab))
    
    # Set initial category
    current_category = "engine"
    category_tabs[0][1].is_active = True

    # Track equipped parts (copy from current car)
    if selected_car is not None:
        equipped_parts = selected_car['parts'].copy()

        # Ensure all categories exist in equipped_parts, but don't auto-fill with defaults
        for category in categories:
            if category not in equipped_parts:
                equipped_parts[category] = None
    else:
        # No selected car, no equipped parts
        equipped_parts = {}
    
    # Create fonts
    header_font = load_font("arial", 48)
    info_font = load_font("arial", 24)

    header = header_font.render(lang_manager.get_text('garage.tuning_garage'), True, (255, 255, 255))

    # Message system for user feedback
    message_text = ""
    message_color = (255, 255, 255)
    message_timer = 0

    # Dialog state for part selling
    sell_part_dialog = None
    selected_part_for_sale = None
    selected_part_category = None

    def set_message(text, color=(255, 255, 255), duration=3.0):
        nonlocal message_text, message_color, message_timer
        message_text = text
        message_color = color
        message_timer = duration

    while run:
        mouse_pos = pygame.mouse.get_pos()

        # Reset cursor state for this frame
        cursor_manager.reset_frame()

        # Update message timer
        if message_timer > 0:
            message_timer -= 1/60  # Assuming 60 FPS

        # Handle events
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                return
            if event.type == pygame.KEYDOWN and event.key == pygame.K_ESCAPE:
                if sell_part_dialog:
                    sell_part_dialog = None
                else:
                    return

            # Handle sell part dialog events
            if sell_part_dialog:
                if event.type == pygame.MOUSEBUTTONDOWN and event.button == 1:
                    dialog_result = sell_part_dialog.handle_event(event, mouse_pos)
                    if dialog_result == "sell":
                        # Sell the part
                        success, selling_price = sell_part(selected_part_for_sale, selected_part_category, car_usage_data)
                        if success:
                            # Reload profile data to get updated inventory and money
                            with open(resource_path('data/profile.json'), 'r', encoding='utf-8') as f:
                                profile_data = json.load(f)
                            # Show confirmation message
                            set_message(f"{lang_manager.get_text('messages.item_sold')}: {selected_part_for_sale['name']} - {selling_price}{lang_manager.get_text('units.currency')}", (100, 255, 100))
                        sell_part_dialog = None
                        selected_part_for_sale = None
                        selected_part_category = None
                    elif dialog_result == "cancel":
                        sell_part_dialog = None
                        selected_part_for_sale = None
                        selected_part_category = None

            # Only handle other events if no dialog is active
            elif not sell_part_dialog:
                # Handle button events
                if back_button.handle_event(event, mouse_pos):
                    return

                if save_button.handle_event(event, mouse_pos):
                    # Add debouncing to save button
                    current_time = pygame.time.get_ticks()
                    if not hasattr(save_car_parts, 'last_save_click_time'):
                        save_car_parts.last_save_click_time = 0

                    if current_time - save_car_parts.last_save_click_time > 400:  # 400ms debounce for saving
                        save_car_parts.last_save_click_time = current_time

                        if selected_car_index != -1:
                            # Validate all equipped parts before saving
                            validation_errors = []
                            for category, part in equipped_parts.items():
                                if part is not None:
                                    is_compatible, msg = validate_part_compatibility(selected_car, part, category)
                                    if not is_compatible:
                                        validation_errors.append(f"{category}: {msg}")

                            if validation_errors:
                                set_message(f"{lang_manager.get_text('messages.error_occurred')} - {len(validation_errors)} validation error(s)", (255, 100, 100))
                            else:
                                success = save_car_parts(selected_car_index, equipped_parts)
                                if success:
                                    # Reload garage data to reflect saved changes
                                    try:
                                        with open(resource_path('data/garage.json'), 'r', encoding='utf-8') as f:
                                            cars_data = json.load(f)
                                        selected_car = cars_data[selected_car_index]
                                        # Update equipped_parts to reflect what was actually saved
                                        equipped_parts = selected_car['parts'].copy()
                                        # Ensure all categories exist
                                        for category in categories:
                                            if category not in equipped_parts:
                                                equipped_parts[category] = None
                                        set_message(lang_manager.get_text('messages.save_successful'), (100, 255, 100))
                                    except Exception as e:
                                        print(f"Error reloading garage data: {e}")
                                        set_message(lang_manager.get_text('messages.save_successful'), (255, 255, 100))
                                else:
                                    set_message(lang_manager.get_text('messages.error_occurred'), (255, 100, 100))

                # Handle refuel button
                if refuel_button.handle_event(event, mouse_pos):
                    if selected_car_index != -1:
                        # Refuel the selected car
                        success, message = fuel_system.refuel_car(selected_car_index)
                        if success:
                            set_message(message, (100, 255, 100))
                            # Reload profile data to reflect money changes
                            try:
                                with open(resource_path('data/profile.json'), 'r', encoding='utf-8') as f:
                                    profile_data = json.load(f)
                            except Exception as e:
                                print(f"Error reloading profile data: {e}")
                        else:
                            set_message(message, (255, 100, 100))
                    else:
                        set_message(lang_manager.get_text('garage.no_car_selected'), (255, 100, 100))

                # Handle category tab events
                for category, tab in category_tabs:
                    if tab.handle_event(event, mouse_pos):
                        current_category = category
                        # Reset all tabs
                        for _, t in category_tabs:
                            t.is_active = False
                        tab.is_active = True

                # Handle part card events
                if event.type == pygame.MOUSEBUTTONDOWN and event.button == 1:
                    # Get owned parts for current category
                    owned_parts = profile_data["inventory"]["owned_parts"].get(current_category, [])
                    available_parts = []

                    # Find parts data for owned parts
                    parts_list = parts_data.get(current_category, [])
                    for part_name in owned_parts:
                        for part in parts_list:
                            if part["name"] == part_name:
                                available_parts.append(part)
                                break

                    # Ensure equipped part is included even if not owned
                    equipped_part = equipped_parts.get(current_category) if equipped_parts else None
                    if equipped_part and all(p["name"] != equipped_part["name"] for p in available_parts):
                        available_parts.append(equipped_part)

                    # Create temporary part cards to check for clicks
                    cards_per_row = 3
                    card_width = 200
                    card_height = 180
                    card_spacing = 20
                    parts_start_x = 400
                    parts_start_y = 250
                    car_usage_data = profile_data.get("usage_data", {}).get("cars", {}).get(str(selected_car_index), valuation_system.get_default_usage_data())

                    for i, part in enumerate(available_parts):
                        row = i // cards_per_row
                        col = i % cards_per_row
                        x = parts_start_x + col * (card_width + card_spacing)
                        y = parts_start_y + row * (card_height + card_spacing)

                        # Check if this part is currently equipped
                        is_equipped = (equipped_part is not None and equipped_part.get("name") == part["name"])

                        card = PartCard(part, x, y, card_width, card_height, is_equipped, car_usage_data)
                        card_action = card.handle_event(event, mouse_pos)

                        if card_action == "equip":
                            # Validate part compatibility before installation
                            is_compatible, compatibility_msg = validate_part_compatibility(selected_car, part, current_category)

                            if not is_compatible:
                                set_message(f"{lang_manager.get_text('messages.error_occurred')}: {compatibility_msg}", (255, 100, 100))
                                continue

                            # If this part is already equipped, unmount it
                            if equipped_parts.get(current_category) and equipped_parts[current_category].get("name") == part["name"]:
                                equipped_parts[current_category] = None
                                set_message(f"{lang_manager.get_text('garage.part_removed')}: {part['name']}", (255, 255, 100))
                            else:
                                # Equip this part (only one per category)
                                equipped_parts[current_category] = part
                                set_message(f"{lang_manager.get_text('garage.part_installed')}: {part['name']} - {lang_manager.get_text('ui.save')}", (100, 255, 100))
                            break

                        elif card_action == "sell" and not is_equipped:
                            # Open sell confirmation dialog
                            part_type = part.get('category', current_category)
                            current_value = valuation_system.calculate_part_value(
                                part,
                                part_type,
                                car_usage_data.get(f"{part_type}_age_days", 0),
                                car_usage_data.get("races_completed", 0)
                            )
                            selling_price = int(current_value * 0.7)  # 70% of current value

                            sell_part_dialog = SellPartDialog(part, selling_price, s_width, s_height)
                            selected_part_for_sale = part
                            selected_part_category = current_category
                            break

        # Handle sell part dialog (legacy update for hover states)
        if sell_part_dialog:
            sell_part_dialog.update(mouse_pos, [False, False, False])  # No clicks through legacy method
            if dialog_result == "sell":
                # Sell the part
                success, selling_price = sell_part(selected_part_for_sale, selected_part_category, car_usage_data)
                if success:
                    # Reload profile data to get updated inventory and money
                    with open(resource_path('data/profile.json'), 'r', encoding='utf-8') as f:
                        profile_data = json.load(f)
                    # Show confirmation message
                    set_message(f"{lang_manager.get_text('messages.item_sold')}: {selected_part_for_sale['name']} - {selling_price}{lang_manager.get_text('units.currency')}", (100, 255, 100))
                sell_part_dialog = None
                selected_part_for_sale = None
                selected_part_category = None
            elif dialog_result == "cancel":
                sell_part_dialog = None
                selected_part_for_sale = None
                selected_part_category = None
        
        # Only update button hover states if no dialog is active
        if not sell_part_dialog:
            # Update button hover states
            back_button.update(mouse_pos)
            save_button.update(mouse_pos)
            refuel_button.update(mouse_pos)

            # Update category tab hover states
            for category, tab in category_tabs:
                tab.update(mouse_pos)
        
            # Get owned parts for current category
            owned_parts = profile_data["inventory"]["owned_parts"].get(current_category, [])
            available_parts = []

            # Find parts data for owned parts
            parts_list = parts_data.get(current_category, [])
            for part_name in owned_parts:
                for part in parts_list:
                    if part["name"] == part_name:
                        available_parts.append(part)
                        break

            # Ensure equipped part is included even if not owned
            equipped_part = equipped_parts.get(current_category) if equipped_parts else None
            if equipped_part and all(p["name"] != equipped_part["name"] for p in available_parts):
                available_parts.append(equipped_part)

            # Create part cards
            part_cards = []
            cards_per_row = 3
            card_width = 200
            card_height = 180  # Increased height to accommodate sell button
            card_spacing = 20
            parts_start_x = 400
            parts_start_y = 250

            # Get usage data for current car
            car_usage_data = profile_data.get("usage_data", {}).get("cars", {}).get(str(selected_car_index), valuation_system.get_default_usage_data())

            for i, part in enumerate(available_parts):
                row = i // cards_per_row
                col = i % cards_per_row
                x = parts_start_x + col * (card_width + card_spacing)
                y = parts_start_y + row * (card_height + card_spacing)

                # Check if this part is currently equipped
                is_equipped = (equipped_part is not None and equipped_part.get("name") == part["name"])

                card = PartCard(part, x, y, card_width, card_height, is_equipped, car_usage_data)
                part_cards.append(card)
        else:
            # If dialog is active, still need to create part cards for display
            owned_parts = profile_data["inventory"]["owned_parts"].get(current_category, [])
            available_parts = []

            # Find parts data for owned parts
            parts_list = parts_data.get(current_category, [])
            for part_name in owned_parts:
                for part in parts_list:
                    if part["name"] == part_name:
                        available_parts.append(part)
                        break

            # Ensure equipped part is included even if not owned
            equipped_part = equipped_parts.get(current_category) if equipped_parts else None
            if equipped_part and all(p["name"] != equipped_part["name"] for p in available_parts):
                available_parts.append(equipped_part)

            # Create part cards (but don't handle clicks)
            part_cards = []
            cards_per_row = 3
            card_width = 200
            card_height = 180
            card_spacing = 20
            parts_start_x = 400
            parts_start_y = 250

            car_usage_data = profile_data.get("usage_data", {}).get("cars", {}).get(str(selected_car_index), valuation_system.get_default_usage_data())

            for i, part in enumerate(available_parts):
                row = i // cards_per_row
                col = i % cards_per_row
                x = parts_start_x + col * (card_width + card_spacing)
                y = parts_start_y + row * (card_height + card_spacing)

                # Check if this part is currently equipped
                is_equipped = (equipped_part is not None and equipped_part.get("name") == part["name"])

                card = PartCard(part, x, y, card_width, card_height, is_equipped, car_usage_data)
                part_cards.append(card)
        
        # Draw everything
        bg.draw(screen)
        
        # Draw header
        screen.blit(header, (s_width // 2 - header.get_width() // 2, 50))
        
        # Draw car card
        if car_card:
            car_card.draw(screen)
        
        # Draw category tabs
        for _, tab in category_tabs:
            tab.draw(screen)
        
        # Draw current category label
        category_label = info_font.render(f"{lang_manager.get_text('parts.category')}: {category_names[current_category]}", True, (255, 255, 255))
        screen.blit(category_label, (parts_start_x, parts_start_y - 30))
        
        # Draw part cards
        for card in part_cards:
            card.draw(screen)

        # Draw performance preview if there are changes
        if selected_car and equipped_parts:
            preview_data = get_installation_preview(selected_car, equipped_parts)
            if preview_data:
                preview_x = parts_start_x
                preview_y = s_height - 180

                # Performance preview background
                preview_rect = pygame.Rect(preview_x, preview_y, 600, 80)
                pygame.draw.rect(screen, (30, 30, 30), preview_rect)
                pygame.draw.rect(screen, (100, 100, 100), preview_rect, 2)

                # Performance preview text
                preview_font = load_font("arial", 18)
                hp_text = preview_font.render(f"{lang_manager.get_text('garage.power')}: {preview_data['total_horsepower']:.0f} {lang_manager.get_text('units.hp')}", True, (255, 255, 255))
                weight_text = preview_font.render(f"{lang_manager.get_text('garage.weight')}: {preview_data['total_weight']:.0f} {lang_manager.get_text('units.kg')}", True, (255, 255, 255))
                ratio_text = preview_font.render(f"{lang_manager.get_text('garage.power_to_weight_ratio')}: {preview_data['power_to_weight_ratio']:.2f}", True, (255, 255, 255))

                # Performance gain indicator
                gain = preview_data['estimated_performance_gain']
                gain_color = (0, 255, 0) if gain > 0 else (255, 0, 0) if gain < 0 else (255, 255, 255)
                gain_text = preview_font.render(f"{lang_manager.get_text('garage.power_change')}: {gain:+.0f} {lang_manager.get_text('units.hp')}", True, gain_color)

                screen.blit(hp_text, (preview_x + 10, preview_y + 10))
                screen.blit(weight_text, (preview_x + 200, preview_y + 10))
                screen.blit(ratio_text, (preview_x + 10, preview_y + 35))
                screen.blit(gain_text, (preview_x + 350, preview_y + 35))

        # Draw buttons
        back_button.draw(screen)
        save_button.draw(screen)
        refuel_button.draw(screen)



        # Show instructions with responsive positioning
        # Calculate safe position for instructions (above performance preview if it exists)
        instruction_y = s_height - 50  # Default position
        if selected_car and equipped_parts:
            preview_data = get_installation_preview(selected_car, equipped_parts)
            if preview_data:
                instruction_y = s_height - 200  # Above performance preview

        instruction = info_font.render("Kliknij na część, aby ją zamontować/odmontować", True, (200, 200, 200))
        save_instruction = info_font.render("Kliknij 'Zapisz zmiany' aby zastosować modyfikacje", True, (255, 255, 0))

        # Create background for instructions for better visibility
        instruction_bg_height = instruction.get_height() + save_instruction.get_height() + 20
        instruction_bg_width = max(instruction.get_width(), save_instruction.get_width()) + 20
        instruction_bg = pygame.Rect(parts_start_x - 10, instruction_y - 30, instruction_bg_width, instruction_bg_height)
        pygame.draw.rect(screen, (0, 0, 0, 180), instruction_bg)
        pygame.draw.rect(screen, (100, 100, 100), instruction_bg, 2)

        screen.blit(instruction, (parts_start_x, instruction_y - 25))
        screen.blit(save_instruction, (parts_start_x, instruction_y))

        # Draw message if active
        if message_timer > 0 and message_text:
            message_surface = info_font.render(message_text, True, message_color)
            message_x = s_width // 2 - message_surface.get_width() // 2
            message_y = 120

            # Message background
            message_bg = pygame.Rect(message_x - 10, message_y - 5, message_surface.get_width() + 20, message_surface.get_height() + 10)
            pygame.draw.rect(screen, (0, 0, 0, 180), message_bg)
            pygame.draw.rect(screen, message_color, message_bg, 2)

            screen.blit(message_surface, (message_x, message_y))

        # Draw sell part dialog if active
        if sell_part_dialog:
            # Draw overlay
            overlay = pygame.Surface((s_width, s_height))
            overlay.set_alpha(128)
            overlay.fill((0, 0, 0))
            screen.blit(overlay, (0, 0))
            sell_part_dialog.draw(screen)

        # Update cursor based on hover states
        cursor_manager.update_cursor()

        pygame.display.update()
